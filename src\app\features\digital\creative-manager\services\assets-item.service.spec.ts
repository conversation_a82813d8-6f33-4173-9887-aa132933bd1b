import { TestBed } from '@angular/core/testing'

import { AssetsItemService } from './assets-item.service'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { LoginHelper } from '@app/shared/utils/login-helper'
import { MatLegacySnackBar } from '@angular/material/legacy-snack-bar'
import { MatLegacyDialogModule } from '@angular/material/legacy-dialog'
import { Globals } from '@app/app.globals'

describe('AssetsItemService', () => {
  let service: AssetsItemService
  class LoginHelperMock {
    isLogged() {
      return true
    }
  }

  const mockGlobals = {
    someMethod: () => {},
  }

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, MatLegacyDialogModule],

      providers: [
        { provide: Globals, useValue: mockGlobals },
        { provide: LoginHelper, useClass: LoginHelperMock },
        {
          provide: MatLegacySnackBar,
          useValue: jasmine.createSpyObj('MatLegacySnackBar', ['open']),
        },
      ],
    })
    service = TestBed.inject(AssetsItemService)
  })

  it('should be created', () => {
    expect(service).toBeTruthy()
  })
})
