import { ComponentFixture, TestBed } from '@angular/core/testing'

import { AsstesInfoComponent } from './asstes-info.component'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { MatLegacySnackBar } from '@angular/material/legacy-snack-bar'
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog'
import { MatLegacyDialogModule } from '@angular/material/legacy-dialog'

describe('AsstesInfoComponent', () => {
  let component: AsstesInfoComponent
  let fixture: ComponentFixture<AsstesInfoComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AsstesInfoComponent],
      imports: [
        HttpClientTestingModule,
        MatDialogModule,
        MatLegacyDialogModule,
      ],
      providers: [
        { provide: MAT_DIALOG_DATA, useValue: {} },
        {
          provide: MatLegacySnackBar,
          useValue: jasmine.createSpyObj('MatLegacySnackBar', ['open']),
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(AsstesInfoComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
