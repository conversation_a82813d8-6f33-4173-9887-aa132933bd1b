module.exports = function (config) {
  const isCI = process.env.CI === 'true'

  config.set({
    basePath: '',
    frameworks: ['jasmine', '@angular-devkit/build-angular'],
    plugins: [
      require('karma-jasmine'),
      require('karma-chrome-launcher'),
      require('karma-edge-launcher'),
      require('karma-jasmine-html-reporter'),
      require('karma-coverage'),
      require('@angular-devkit/build-angular/plugins/karma'),
    ],
    client: {
      clearContext: false, // mantém os resultados no navegador
    },
    coverageReporter: {
      dir: require('path').join(__dirname, './coverage'),
      subdir: '.',
      reporters: [
        { type: 'html' }, // abre no navegador
        { type: 'text-summary' }, // exibe no terminal
        { type: 'json-summary' },
      ],
      fixWebpackSourcePaths: true,
    },
    reporters: ['progress', 'kjhtml', 'coverage'],
    port: 9876,
    colors: true,
    logLevel: config.LOG_INFO,
    customLaunchers: {
      ChromeHeadlessNoSandbox: {
        base: 'ChromeHeadless',
        flags: ['--no-sandbox', '--disable-gpu'],
      },
      EdgeHeadlessNoSandbox: {
        base: 'EdgeHeadless',
        flags: ['--no-sandbox', '--disable-gpu'],
      },
    },
    browsers: [isCI ? 'EdgeHeadlessNoSandbox' : 'Edge'],
    autoWatch: isCI,
    singleRun: true,
    files: [{ pattern: './test.ts', watched: false }],
    webpack: {
      resolve: {
        fallback: {
          process: require.resolve('process/browser'),
        },
      },
      plugins: [
        new (require('webpack').ProvidePlugin)({
          process: 'process/browser',
        }),
      ],
    },
  })
}
