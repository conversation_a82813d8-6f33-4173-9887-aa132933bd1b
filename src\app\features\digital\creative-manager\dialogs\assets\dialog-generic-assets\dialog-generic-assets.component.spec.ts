import { ComponentFixture, TestBed } from '@angular/core/testing'

import { DialogGenericAssetsComponent } from './dialog-generic-assets.component'
import { NO_ERRORS_SCHEMA } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'

describe('DialogGenericAssetsComponent', () => {
  let component: DialogGenericAssetsComponent
  let fixture: ComponentFixture<DialogGenericAssetsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogGenericAssetsComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        {
          provide: MatDialogRef,
          useValue: jasmine.createSpyObj('MatDialogRef', ['close']),
        },
        { provide: MAT_DIALOG_DATA, useValue: {} },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DialogGenericAssetsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
